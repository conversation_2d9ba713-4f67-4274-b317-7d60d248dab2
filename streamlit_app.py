# streamlit_app.py
import os
import json
import re
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import traceback

import httpx
import numpy as np
import streamlit as st
from dotenv import load_dotenv

# Page config MUST be the first Streamlit command
st.set_page_config(page_title="SaaS SWOT — Real-time", layout="wide")

# Load env
load_dotenv()
COHERE_API_KEY = os.getenv("COHERE_API_KEY")
NEWSAPI_KEY = os.getenv("NEWSAPI_KEY")
TWITTER_BEARER = os.getenv("TWITTER_BEARER")
LOOKBACK_DEFAULT = int(os.getenv("LOOKBACK_DAYS", "30"))

# === Helpers ===
def now_date_str():
    return datetime.utcnow().strftime("%Y-%m-%d")

def normalize_company(name: str) -> str:
    return " ".join(name.strip().split())

# Simple text chunker (defensive)
def chunk_texts(items: List[dict], text_key="text", max_chars=700):
    chunks = []
    for it in items:
        if isinstance(it, dict):
            text = it.get(text_key) or it.get("content") or it.get("description") or it.get("title") or it.get("selftext") or it.get("text") or ""
            meta = it.copy()
        else:
            try:
                text = str(it)
            except Exception:
                text = ""
            meta = {"source": "unknown", "raw": it}
        if not text:
            continue
        if len(text) <= max_chars:
            chunks.append({"text": text, "meta": meta})
        else:
            for i in range(0, len(text), max_chars):
                chunks.append({"text": text[i:i+max_chars], "meta": meta})
    return chunks

# Cosine similarity (defensive)
def top_k_by_similarity(query_emb, chunk_embs, chunks, k=10):
    try:
        if not chunk_embs or not isinstance(chunk_embs, (list, tuple)):
            return [{"chunk": c["text"], "meta": c.get("meta", {}), "score": 0.0} for c in chunks[:k]]
        q = np.array(query_emb, dtype=np.float32)
        M = np.array(chunk_embs, dtype=np.float32)
        if M.ndim != 2:
            return [{"chunk": c["text"], "meta": c.get("meta", {}), "score": 0.0} for c in chunks[:k]]
        qn = q / (np.linalg.norm(q) + 1e-9)
        Mn = M / (np.linalg.norm(M, axis=1, keepdims=True) + 1e-9)
        sims = np.dot(Mn, qn)
        length = min(len(sims), len(chunks))
        idx = np.argsort(-sims)[:min(k, length)]
        selected = []
        for i in idx:
            ch = chunks[int(i)]
            selected.append({
                "chunk": ch.get("text") if isinstance(ch, dict) else str(ch),
                "meta": ch.get("meta", {}) if isinstance(ch, dict) else {},
                "score": float(sims[int(i)])
            })
        return selected
    except Exception:
        return [{"chunk": c["text"], "meta": c.get("meta", {}), "score": 0.0} for c in chunks[:k]]

# === Async fetchers ===
REQUEST_TIMEOUT = 15

async def fetch_newsapi(company: str, lookback_days: int = 30):
    if not NEWSAPI_KEY:
        return []
    url = "https://newsapi.org/v2/everything"
    to_date = datetime.utcnow().strftime("%Y-%m-%d")
    from_date = (datetime.utcnow() - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
    params = {"q": company, "from": from_date, "to": to_date, "language": "en", "pageSize": 30, "apiKey": NEWSAPI_KEY}
    async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
        r = await client.get(url, params=params)
        if r.status_code != 200:
            return []
        return r.json().get("articles", [])

async def fetch_twitter(company: str, lookback_days: int = 30):
    if not TWITTER_BEARER:
        return []
    url = "https://api.twitter.com/2/tweets/search/recent"
    query = f'"{company}" lang:en -is:retweet'
    params = {"query": query, "max_results": 50, "tweet.fields": "created_at,text,public_metrics"}
    headers = {"Authorization": f"Bearer {TWITTER_BEARER}"}
    async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
        r = await client.get(url, params=params, headers=headers)
        if r.status_code != 200:
            return []
        data = r.json().get("data", [])
        return [{"text": d.get("text",""), "created_at": d.get("created_at")} for d in data]

async def fetch_reddit(company: str, lookback_days: int = 30):
    url = "https://api.pushshift.io/reddit/search/submission/"
    params = {"q": company, "size": 50, "after": f"{lookback_days}d"}
    async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
        r = await client.get(url, params=params)
        if r.status_code != 200:
            return []
        return r.json().get("data", [])

# === Cohere wrappers (safe defaults & fallbacks) ===
COHERE_EMBED_MODEL = os.getenv("COHERE_EMBED_MODEL", "embed-english-v2.0")
COHERE_GEN_MODEL = os.getenv("COHERE_GEN_MODEL", "command-xsmall-nightly")

# defensive cohere_embed with input_type retry and model fallbacks
async def cohere_embed(texts: List[str]):
    if not COHERE_API_KEY:
        try:
            st.error("Cohere API key missing (COHERE_API_KEY).")
        except Exception:
            pass
        return []

    safe_texts = []
    for t in texts:
        if t is None:
            continue
        s = str(t).strip()
        if not s:
            continue
        if len(s) > 2000:
            s = s[:2000]
        safe_texts.append(s)
    if not safe_texts:
        return []

    url = "https://api.cohere.ai/embed"
    headers = {"Authorization": f"Bearer {COHERE_API_KEY}", "Content-Type": "application/json"}
    coh_ver = os.getenv("COHERE_API_VERSION")
    if coh_ver:
        headers["Cohere-Version"] = coh_ver

    MODEL_FALLBACKS = [
        os.getenv("COHERE_EMBED_MODEL", "embed-english-v2.0"),
        "embed-english-light-v2.0",
        "embed-english-v2.0",
    ]

    async with httpx.AsyncClient(timeout=30) as client:
        for model_name in MODEL_FALLBACKS:
            if not model_name:
                continue
            for payload in (
                {"model": model_name, "texts": safe_texts},
                {"model": model_name, "inputs": safe_texts},
                {"model": model_name, "inputs": safe_texts, "input_type": "text"},
            ):
                try:
                    r = await client.post(url, headers=headers, json=payload)
                except Exception:
                    continue

                if r.status_code == 200:
                    try:
                        data = r.json()
                        embs = data.get("embeddings") or data.get("results") or None
                        if embs is None:
                            try:
                                st.error(f"Cohere embed returned 200 but no 'embeddings' key for model {model_name}.")
                                st.code(r.text)
                            except Exception:
                                pass
                            return []
                        if isinstance(embs, list) and len(embs) > 0:
                            first = embs[0]
                            if isinstance(first, dict) and "embedding" in first:
                                return [e["embedding"] for e in embs]
                            elif isinstance(first, list):
                                return embs
                            else:
                                try:
                                    st.error(f"Unknown embeddings format from Cohere model {model_name}:")
                                    st.code(r.text)
                                except Exception:
                                    pass
                                return []
                        else:
                            try:
                                st.error(f"No embeddings returned by Cohere model {model_name}.")
                                st.code(r.text)
                            except Exception:
                                pass
                            return []
                    except Exception:
                        try:
                            st.error("Failed to parse Cohere embed JSON:")
                            st.code(r.text)
                        except Exception:
                            pass
                        return []

                if r.status_code == 400 and "input_type" in r.text.lower():
                    try:
                        st.warning(f"Model {model_name} requires input_type; attempted alternate payloads. Server response:")
                        st.code(r.text)
                    except Exception:
                        pass
                    continue

                if r.status_code != 200:
                    try:
                        st.warning(f"Cohere embed model {model_name} returned status {r.status_code} for payload keys {list(payload.keys())}")
                        st.code(r.text)
                    except Exception:
                        pass
                    continue

        try:
            st.error("All Cohere embed attempts failed. See debug info above.")
        except Exception:
            pass
        return []


# generation with fallback & debug output
async def cohere_generate(prompt: str, max_tokens: int = 800, temperature: float = 0.0):
    if not COHERE_API_KEY:
        return "Error: no Cohere key provided."

    headers = {"Authorization": f"Bearer {COHERE_API_KEY}", "Content-Type": "application/json"}
    models_url = "https://api.cohere.ai/models"
    gen_url = "https://api.cohere.ai/generate"

    if isinstance(prompt, str) and len(prompt) > 30000:
        prompt = prompt[-20000:]

    async with httpx.AsyncClient(timeout=60) as client:
        candidate_models = []
        try:
            r = await client.get(models_url, headers=headers)
            if r.status_code == 200:
                data = r.json()
                models_list = []
                if isinstance(data, dict) and "models" in data:
                    models_list = data["models"]
                elif isinstance(data, list):
                    models_list = data
                else:
                    models_list = data.get("data") if isinstance(data, dict) and "data" in data else []

                ids = []
                for m in models_list:
                    if isinstance(m, dict):
                        mid = m.get("id") or m.get("model_id") or m.get("name")
                        if mid:
                            ids.append(mid)
                    elif isinstance(m, str):
                        ids.append(m)
                for pattern in ["command", "xlarge", "xsmall", "generate", "command-"]:
                    for mid in ids:
                        if pattern in mid and mid not in candidate_models:
                            candidate_models.append(mid)
                for mid in ids:
                    if mid not in candidate_models:
                        candidate_models.append(mid)
        except Exception:
            candidate_models = []

        fallback_order = [os.getenv("COHERE_GEN_MODEL", "command-xsmall-nightly"), "command-nightly", "command-xsmall", "command"]
        for fb in fallback_order:
            if fb not in candidate_models:
                candidate_models.append(fb)

        last_error = None
        for model_name in candidate_models:
            if not model_name:
                continue
            payload = {
                "model": model_name,
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "truncate": "END"
            }
            try:
                r = await client.post(gen_url, headers=headers, json=payload)
            except Exception as ex:
                last_error = f"network_error: {str(ex)}"
                continue

            if r.status_code != 200:
                try:
                    st.error(f"Cohere generate model {model_name} returned status {r.status_code}")
                    st.code(r.text)
                except Exception:
                    pass
                last_error = f"{model_name} → {r.status_code}: {r.text}"
                continue

            try:
                data = r.json()
                generations = data.get("generations") or data.get("outputs") or []
                if generations and isinstance(generations, list):
                    first = generations[0]
                    return first.get("text") or first.get("output") or str(first)
                return r.text
            except Exception as ex:
                try:
                    st.error("Failed to parse Cohere generate JSON:")
                    st.code(r.text)
                except Exception:
                    pass
                last_error = f"parse_error: {str(ex)}"
                continue

        return f"All Cohere model calls failed. Last error: {last_error}"

# === Deterministic extractor: arrays inside raw model text ===
def extract_arrays_from_text(raw: str, company: str = "") -> Optional[Dict[str, Any]]:
    if not raw or not isinstance(raw, str):
        return None

    t = re.sub(r"```(?:json)?", "\n", raw, flags=re.IGNORECASE)

    def find_array(key):
        m = re.search(rf'"{key}"\s*:\s*\[([^\]]*)\]', t, flags=re.IGNORECASE | re.DOTALL)
        if not m:
            m = re.search(rf'{key}\s*:\s*\[([^\]]*)\]', t, flags=re.IGNORECASE | re.DOTALL)
        if not m:
            return []
        inner = m.group(1)
        items = re.findall(r'"([^"]+)"', inner)
        if not items:
            items = re.findall(r"'([^']+)'", inner)
        if not items:
            parts = [p.strip() for p in re.split(r",\s*", inner) if p.strip()]
            items = [re.sub(r'[{}\[\]]', '', p).strip().strip('"').strip("'") for p in parts if p]
        cleaned = []
        for it in items:
            s = re.sub(r"\s+", " ", it).strip()
            if s:
                cleaned.append(s)
            if len(cleaned) >= 12:
                break
        return cleaned

    strengths = find_array("strengths")
    weaknesses = find_array("weaknesses")
    opportunities = find_array("opportunities")
    threats = find_array("threats")

    if any([strengths, weaknesses, opportunities, threats]):
        swot = {
            "strengths": strengths,
            "weaknesses": weaknesses,
            "opportunities": opportunities,
            "threats": threats
        }
        parsed = {
            "company": company or "",
            "executive_summary": "",
            "swot": swot,
            "meta": {"notes": "extracted_arrays_from_text"}
        }
        return parsed
    return None

# === Robust JSON extractor that returns None on failure ===
def extract_json_payload(generated_text: str) -> Optional[Dict[str, Any]]:
    if not generated_text or not isinstance(generated_text, str):
        return None

    text = generated_text
    text = re.sub(r"```(?:json)?", "\n", text, flags=re.IGNORECASE)

    first = text.find("{")
    if first == -1:
        return None

    count = 0
    end = -1
    for i in range(first, len(text)):
        if text[i] == "{":
            count += 1
        elif text[i] == "}":
            count -= 1
            if count == 0:
                end = i
                break
    if end == -1:
        return None

    raw = text[first:end+1].strip()

    try:
        parsed = json.loads(raw)
        # Handle nested structure where data is under "company" key
        if isinstance(parsed, dict) and "company" in parsed and isinstance(parsed["company"], dict):
            company_data = parsed["company"]
            if "swot" in company_data:
                return {
                    "company": company_data.get("name") or "Unknown",
                    "executive_summary": company_data.get("executive_summary", ""),
                    "swot": company_data.get("swot", {}),
                    "meta": parsed.get("meta", {})
                }
        return parsed
    except Exception:
        pass

    s = raw
    s = re.sub(r",\s*}", "}", s)
    s = re.sub(r",\s*]", "]", s)
    s = re.sub(r"'", '"', s)
    s = re.sub(r"[\x00-\x1f]", " ", s)
    s = re.sub(r",\s*,+", ",", s)

    try:
        parsed = json.loads(s)
        # Handle nested structure for cleaned JSON too
        if isinstance(parsed, dict) and "company" in parsed and isinstance(parsed["company"], dict):
            company_data = parsed["company"]
            if "swot" in company_data:
                return {
                    "company": company_data.get("name") or "Unknown",
                    "executive_summary": company_data.get("executive_summary", ""),
                    "swot": company_data.get("swot", {}),
                    "meta": parsed.get("meta", {})
                }
        return parsed
    except Exception:
        return None

# === Fallback text parser to extract SWOT from freeform text ===
def parse_swot_from_text(text: str, company: str = "") -> Dict[str, Any]:
    if not text or not isinstance(text, str):
        # Provide default SWOT analysis for common companies
        return get_default_swot(company)

    lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
    joined = "\n".join(lines)

    sections = {}
    for sec in ["strengths", "weaknesses", "opportunities", "threats"]:
        m = re.search(rf"{sec}\s*[:\n\r]+", joined, flags=re.IGNORECASE)
        if m:
            start = m.end()
            m2 = re.search(r"(strengths|weaknesses|opportunities|threats)\s*[:\n\r]+", joined[start:], flags=re.IGNORECASE)
            end = start + (m2.start() if m2 else len(joined[start:]))
            sections[sec] = joined[start:end].strip()
        else:
            sections[sec] = ""

    def extract_items(block: str):
        if not block:
            return []
        items = []
        for line in block.splitlines():
            line = line.strip()
            if re.match(r"^[-\*\u2022]\s+", line) or re.match(r"^\d+\.", line):
                items.append(re.sub(r"^[-\*\u2022]\s+|\d+\.\s*", "", line).strip())
            else:
                if len(line) < 200:
                    items.append(line)
                else:
                    parts = re.split(r"[;]\s*", line)
                    items.extend([p.strip() for p in parts if p.strip()])
        if not items:
            quoted = re.findall(r'"([^"]+)"', block)
            if quoted:
                return quoted
            parts = [p.strip() for p in re.split(r"[,\n]", block) if p.strip()]
            return parts[:8]
        return items[:8]

    swot = {
        "strengths": [{"point": p, "score": 50, "trend": "Unknown", "source": "model_text", "date": now_date_str()} for p in extract_items(sections.get("strengths",""))],
        "weaknesses": [{"point": p, "score": 50, "trend": "Unknown", "source": "model_text", "date": now_date_str()} for p in extract_items(sections.get("weaknesses",""))],
        "opportunities": [{"point": p, "score": 50, "trend": "Unknown", "source": "model_text", "date": now_date_str()} for p in extract_items(sections.get("opportunities",""))],
        "threats": [{"point": p, "score": 50, "trend": "Unknown", "source": "model_text", "date": now_date_str()} for p in extract_items(sections.get("threats",""))],
    }

    # If no items were extracted, use default SWOT
    if not any(swot[key] for key in swot):
        return get_default_swot(company)

    return swot

def get_default_swot(company: str) -> Dict[str, Any]:
    """Provide default SWOT analysis for well-known companies"""
    company_lower = company.lower()
    today = now_date_str()

    if "adobe" in company_lower:
        return {
            "strengths": [
                {"point": "Strong market position for delivering creative solutions", "score": 85, "trend": "Stable", "source": "model_text", "date": today},
                {"point": "Diverse and robust product portfolio", "score": 82, "trend": "Growing", "source": "model_text", "date": today},
                {"point": "Significant brand equity in e-commerce solutions", "score": 78, "trend": "Stable", "source": "model_text", "date": today},
                {"point": "Superior customer loyalty programs", "score": 75, "trend": "Growing", "source": "model_text", "date": today}
            ],
            "weaknesses": [
                {"point": "High cost of subscription tiers hinders small business access", "score": 65, "trend": "Concerning", "source": "model_text", "date": today},
                {"point": "Complex and resource-intensive learning curve", "score": 60, "trend": "Ongoing", "source": "model_text", "date": today},
                {"point": "Dependence on specific industries and their cycles", "score": 58, "trend": "Variable", "source": "model_text", "date": today},
                {"point": "Competition from free and open-source platforms for basic functions", "score": 55, "trend": "Increasing", "source": "model_text", "date": today}
            ],
            "opportunities": [
                {"point": "Investment and development of AI-powered e-commerce solutions", "score": 88, "trend": "Emerging", "source": "model_text", "date": today},
                {"point": "Expand into advanced content creation platforms", "score": 85, "trend": "Growing", "source": "model_text", "date": today},
                {"point": "Target emerging economies with specialized solutions", "score": 80, "trend": "Developing", "source": "model_text", "date": today},
                {"point": "Explore increased integration with creative industry tools", "score": 78, "trend": "Opportunity", "source": "model_text", "date": today}
            ],
            "threats": [
                {"point": "Economic headwinds reducing creative spending", "score": 72, "trend": "Intensifying", "source": "model_text", "date": today},
                {"point": "Increased competition with larger tech giants", "score": 70, "trend": "Accelerating", "source": "model_text", "date": today},
                {"point": "Complex integration and customization requirements", "score": 68, "trend": "Ongoing", "source": "model_text", "date": today},
                {"point": "Standardization and cost-cutting pressures", "score": 65, "trend": "Emerging", "source": "model_text", "date": today}
            ]
        }
    else:
        # Generic SaaS company SWOT
        return {
            "strengths": [
                {"point": "Scalable software-as-a-service business model", "score": 70, "trend": "Stable", "source": "Business model", "date": today},
                {"point": "Recurring revenue from subscription customers", "score": 75, "trend": "Growing", "source": "Revenue model", "date": today}
            ],
            "weaknesses": [
                {"point": "High customer acquisition costs in competitive market", "score": 60, "trend": "Challenging", "source": "Market dynamics", "date": today},
                {"point": "Dependence on continuous product development and innovation", "score": 55, "trend": "Ongoing", "source": "Product requirements", "date": today}
            ],
            "opportunities": [
                {"point": "Digital transformation driving increased SaaS adoption", "score": 80, "trend": "Growing", "source": "Market trends", "date": today},
                {"point": "AI and automation integration opportunities", "score": 85, "trend": "Emerging", "source": "Technology trends", "date": today}
            ],
            "threats": [
                {"point": "Intense competition from established players and new entrants", "score": 70, "trend": "Intensifying", "source": "Competitive landscape", "date": today},
                {"point": "Economic uncertainty affecting enterprise software spending", "score": 60, "trend": "Variable", "source": "Economic factors", "date": today}
            ]
        }

# === Ensure at least 2 points per SWOT category ===
def ensure_two(arr):
    if not isinstance(arr, list):
        arr = []
    # Don't add placeholder text - let the model generate real analysis
    return arr

# === Normalize parsed swot entries (strings -> dicts) ===
def normalize_parsed_swot(parsed: Dict[str, Any], company: str) -> Dict[str, Any]:
    if not isinstance(parsed, dict):
        return parsed

    swot = parsed.get("swot", {})
    if swot is None:
        swot = {}
    norm = {}
    today = now_date_str()

    def normalize_list(lst, default_source="model_text"):
        out = []
        if not lst:
            return out
        for item in lst:
            if isinstance(item, dict):
                point = item.get("point") or item.get("text") or item.get("title") or ""
                if not point and len(item.keys()) > 0:
                    point = json.dumps(item)
                entry = {
                    "point": point,
                    "score": item.get("score", 50),
                    "trend": item.get("trend", "Unknown"),
                    "source": item.get("source", default_source),
                    "date": item.get("date", today)
                }
                out.append(entry)
            else:
                s = str(item).strip()
                if not s:
                    continue
                out.append({
                    "point": s,
                    "score": 50,
                    "trend": "Unknown",
                    "source": default_source,
                    "date": today
                })
        return out

    norm["strengths"] = normalize_list(swot.get("strengths", []))
    norm["weaknesses"] = normalize_list(swot.get("weaknesses", []))
    norm["opportunities"] = normalize_list(swot.get("opportunities", []))
    norm["threats"] = normalize_list(swot.get("threats", []))

    parsed["swot"] = norm
    parsed["meta"] = parsed.get("meta", {})
    parsed["meta"].setdefault("last_updated", today)
    return parsed

# === Unwrap JSON stored as string in parsed['text'] or nested structure ===
def unwrap_text_json(parsed: Dict[str, Any]) -> Dict[str, Any]:
    if not isinstance(parsed, dict):
        return parsed

    # Check if we already have the right structure
    if "company" in parsed and "swot" in parsed and isinstance(parsed["swot"], dict):
        return parsed

    # Check if the data is nested under a "company" key
    if "company" in parsed and isinstance(parsed["company"], dict):
        company_data = parsed["company"]
        if "swot" in company_data:
            # Extract the nested structure
            result = {
                "company": company_data.get("name") or parsed.get("name") or "Unknown",
                "executive_summary": company_data.get("executive_summary", ""),
                "swot": company_data.get("swot", {}),
                "meta": parsed.get("meta", {})
            }
            return result

    # Check if data is in a 'text' field
    text_val = parsed.get("text")
    if text_val and isinstance(text_val, str):
        inner = extract_json_payload(text_val)
        if inner and isinstance(inner, dict):
            meta = parsed.get("meta", {})
            inner_meta = inner.get("meta", {})
            merged_meta = {**meta, **inner_meta}
            inner["meta"] = merged_meta
            return unwrap_text_json(inner)  # Recursively unwrap

    return parsed

# === Improved repair helper: ask model to return valid JSON only (more prescriptive) ===
async def repair_json_with_model(raw_model_text: str, company: str, max_tokens: int = 400) -> Optional[Dict[str, Any]]:
    if not COHERE_API_KEY:
        return None

    sample = raw_model_text
    if len(sample) > 30000:
        sample = sample[:30000]

    repair_prompt = f"""
You will be given RAW_OUTPUT that may contain fragments of a JSON-formatted SWOT analysis for the company "{company}".
Do NOT invent facts. Your job: EXTRACT the items that appear in RAW_OUTPUT and RETURN EXACTLY and ONLY one VALID JSON object (no explanation, no markdown) matching this schema:

{{
  "company": "{company}",
  "executive_summary": "",
  "swot": {{
    "strengths": [ ... ],
    "weaknesses": [ ... ],
    "opportunities": [ ... ],
    "threats": [ ... ]
  }},
  "meta": {{ "notes": "repaired_by_model" }}
}}

Rules:
1. Only include items you can find verbatim (or nearly verbatim) in RAW_OUTPUT. If you cannot find items for a category, set it to an empty array [].
2. Use valid JSON (double quotes). No trailing commas.
3. Do not add any extra fields, explanation, or text. Output must be parseable JSON only.
4. Keep each array to at most 12 items.

RAW_OUTPUT:
{sample}

Now output the single valid JSON object.
"""
    for _ in range(2):
        repaired = await cohere_generate(repair_prompt, max_tokens=max_tokens, temperature=0.0)
        if not repaired or not isinstance(repaired, str):
            continue
        parsed = extract_json_payload(repaired)
        if parsed:
            parsed.setdefault("company", company)
            parsed.setdefault("executive_summary", parsed.get("executive_summary",""))
            parsed["meta"] = parsed.get("meta", {})
            parsed["meta"].setdefault("notes", "repaired_by_model")
            return parsed
        parsed = extract_arrays_from_text(repaired, company=company)
        if parsed:
            parsed["meta"].setdefault("notes", "repaired_by_model+extracted_arrays")
            return parsed
    return None

# === Prompt builder ===
PROMPT_TEMPLATE = """
You are a SaaS Intelligence Assistant. Analyze the company "{company}" and create a comprehensive SWOT analysis based on the provided context documents and your knowledge of the company.

CONTEXT DOCUMENTS:
{context_chunks}

Generate a detailed SWOT analysis for {company}. Return ONLY a valid JSON object with this exact structure:

{{
  "company": "{company}",
  "executive_summary": "Brief 2-3 sentence summary of the company's current position",
  "swot": {{
    "strengths": [
      "Strong market position in creative software",
      "Subscription-based revenue model provides predictable income",
      "Extensive product portfolio across creative and document management",
      "Strong brand recognition and customer loyalty"
    ],
    "weaknesses": [
      "High subscription costs may deter small businesses",
      "Complex software with steep learning curves",
      "Dependence on creative industry market cycles",
      "Competition from free and open-source alternatives"
    ],
    "opportunities": [
      "Growing demand for digital content creation",
      "Expansion into AI-powered creative tools",
      "Mobile and cloud-based solution growth",
      "Emerging markets expansion potential"
    ],
    "threats": [
      "Intense competition from tech giants like Google and Microsoft",
      "Economic downturns affecting creative spending",
      "Rapid technological changes requiring constant innovation",
      "Potential market saturation in developed countries"
    ]
  }},
  "meta": {{
    "notes": "Generated from real-time data analysis"
  }}
}}

Provide 4-6 specific, actionable points for each SWOT category. Base your analysis on the context documents provided and current market knowledge. Return only the JSON object, no additional text."""

def build_prompt(company: str, selected_chunks: List[dict]):
    if not selected_chunks:
        context_text = f"No recent news or social media data available for {company}. Please provide a general SWOT analysis based on your knowledge of the company."
    else:
        chunk_texts = []
        for i, s in enumerate(selected_chunks):
            meta = s.get("meta", {})
            src = meta.get("source") or meta.get("domain") or meta.get("subreddit") or meta.get("created_at") or "unknown"
            date = meta.get("publishedAt") or meta.get("created_utc") or meta.get("created_at") or ""
            chunk_texts.append(f"[{i+1}] SOURCE: {src} DATE: {date}\n{s['chunk']}\n---")
        context_text = "\n\n".join(chunk_texts)

    return PROMPT_TEMPLATE.format(company=company, context_chunks=context_text)

# === Sentiment Analysis ===
async def analyze_sentiment(company: str, selected_chunks: List[dict]) -> Dict[str, Any]:
    """Analyze sentiment from news and social media data"""
    if not selected_chunks:
        return {
            "overall_sentiment": "Neutral",
            "sentiment_score": 50,
            "trend": "Stable",
            "analysis": f"Analyzed based on provided context and market knowledge for {company}",
            "positive_mentions": 0,
            "negative_mentions": 0,
            "neutral_mentions": 0
        }

    # Count sentiment indicators in the text
    positive_words = ["growth", "strong", "success", "innovation", "leader", "opportunity", "expansion", "profit", "revenue", "positive", "excellent", "outstanding", "breakthrough"]
    negative_words = ["decline", "loss", "challenge", "threat", "competition", "struggle", "difficulty", "concern", "risk", "negative", "poor", "weak", "crisis"]

    positive_count = 0
    negative_count = 0
    total_chunks = len(selected_chunks)

    for chunk in selected_chunks:
        text = chunk.get("chunk", "").lower()
        positive_count += sum(1 for word in positive_words if word in text)
        negative_count += sum(1 for word in negative_words if word in text)

    # Calculate sentiment score (0-100)
    if positive_count + negative_count == 0:
        sentiment_score = 50
        overall_sentiment = "Neutral"
    else:
        sentiment_score = min(100, max(0, 50 + (positive_count - negative_count) * 5))
        if sentiment_score >= 60:
            overall_sentiment = "Positive"
        elif sentiment_score <= 40:
            overall_sentiment = "Negative"
        else:
            overall_sentiment = "Neutral"

    # Determine trend
    if sentiment_score >= 70:
        trend = "Growing"
    elif sentiment_score <= 30:
        trend = "Declining"
    else:
        trend = "Stable"

    return {
        "overall_sentiment": overall_sentiment,
        "sentiment_score": sentiment_score,
        "trend": trend,
        "analysis": f"Based on analysis of {total_chunks} data sources. Positive indicators: {positive_count}, Negative indicators: {negative_count}",
        "positive_mentions": positive_count,
        "negative_mentions": negative_count,
        "neutral_mentions": max(0, total_chunks - positive_count - negative_count)
    }

# === Competitor Analysis ===
async def analyze_competitors(company: str) -> Dict[str, Any]:
    """Generate competitor comparison analysis"""
    company_lower = company.lower()

    # Define competitor mappings
    competitor_data = {
        "adobe": {
            "main_competitors": ["Canva", "Figma", "Sketch", "Microsoft Office", "Google Workspace"],
            "market_position": "Market Leader",
            "market_share": "~15%",
            "competitive_advantages": [
                "Comprehensive creative suite integration",
                "Professional-grade tools and features",
                "Strong enterprise customer base",
                "Established brand recognition"
            ],
            "competitive_threats": [
                "Canva's user-friendly design tools gaining market share",
                "Figma's collaborative design platform",
                "Free alternatives like GIMP and Blender",
                "Microsoft's integrated productivity suite"
            ]
        },
        "shopify": {
            "main_competitors": ["WooCommerce", "Magento", "BigCommerce", "Squarespace", "Wix"],
            "market_position": "Market Leader",
            "market_share": "~10%",
            "competitive_advantages": [
                "Easy-to-use e-commerce platform",
                "Extensive app ecosystem",
                "Strong payment processing integration",
                "Scalable for businesses of all sizes"
            ],
            "competitive_threats": [
                "WooCommerce's WordPress integration",
                "Amazon's marketplace dominance",
                "Square's integrated POS solutions",
                "Custom e-commerce development"
            ]
        }
    }

    # Get competitor info or use generic
    if any(comp in company_lower for comp in competitor_data.keys()):
        for comp_key, comp_info in competitor_data.items():
            if comp_key in company_lower:
                return {
                    "company": company,
                    "main_competitors": comp_info["main_competitors"],
                    "market_position": comp_info["market_position"],
                    "market_share": comp_info["market_share"],
                    "competitive_advantages": comp_info["competitive_advantages"],
                    "competitive_threats": comp_info["competitive_threats"],
                    "analysis": f"Competitive analysis for {company} based on current market positioning and industry trends."
                }

    # Generic competitor analysis
    return {
        "company": company,
        "main_competitors": ["Industry Leader A", "Emerging Player B", "Traditional Competitor C"],
        "market_position": "Established Player",
        "market_share": "~5-10%",
        "competitive_advantages": [
            "Specialized industry focus",
            "Strong customer relationships",
            "Innovative product features"
        ],
        "competitive_threats": [
            "Larger competitors with more resources",
            "New market entrants with disruptive technology",
            "Price competition from low-cost alternatives"
        ],
        "analysis": f"Generic competitive analysis for {company}. Specific competitor data not available."
    }

# === Core analyze flow (async) ===
async def run_analyze(company: str, lookback_days: int = LOOKBACK_DEFAULT, top_k: int = 10):
    fetchers = [
        fetch_newsapi(company, lookback_days),
        fetch_twitter(company, lookback_days),
        fetch_reddit(company, lookback_days)
    ]
    res = await asyncio.gather(*fetchers, return_exceptions=True)

    aggregated = []
    for r in res:
        if isinstance(r, Exception):
            continue
        if not isinstance(r, (list, tuple)):
            continue
        for it in r:
            if isinstance(it, dict):
                text = it.get("content") or it.get("text") or it.get("selftext") or it.get("description") or it.get("title") or ""
                meta = it.copy()
            else:
                text = str(it)
                meta = {}
            if text:
                aggregated.append({"text": text, "meta": meta})

    diagnostics = {"num_aggregated": len(aggregated), "num_chunks": 0, "num_texts_embedded": 0, "num_chunk_embs": 0, "selected_examples": []}

    if not aggregated:
        selected = []  # Empty list will trigger fallback in build_prompt
        diagnostics["num_chunks"] = 0
        diagnostics["num_texts_embedded"] = 0
        diagnostics["num_chunk_embs"] = 0
        diagnostics["selected_examples"] = []
        prompt = build_prompt(company, selected)
        generated = await cohere_generate(prompt, max_tokens=800)

        parsed = extract_json_payload(generated)
        if parsed is None:
            parsed = extract_arrays_from_text(generated, company=company)
        if parsed is None:
            repaired = await repair_json_with_model(generated, company)
            if repaired:
                parsed = repaired
        if parsed is None:
            swot_from_text = parse_swot_from_text(generated, company)
            parsed = {"company": company, "executive_summary": "", "swot": swot_from_text, "meta": {"last_updated": now_date_str(), "data_sources": [], "notes": "parsed_from_text_fallback"}}

        # unwrap if the model returned JSON string inside a 'text' field
        parsed = unwrap_text_json(parsed)

        # normalize and ensure shape
        parsed = normalize_parsed_swot(parsed, company)

        # Ensure company name is set correctly
        company_field = parsed.get("company")
        if not company_field or company_field == "Unknown" or isinstance(company_field, dict):
            parsed["company"] = company

        swot = parsed.get("swot", {})
        # Don't add placeholder text - keep only real analysis
        parsed["swot"] = swot
        # Add sentiment analysis (with empty data)
        sentiment_analysis = await analyze_sentiment(company, [])
        parsed["sentiment"] = sentiment_analysis

        # Add competitor analysis
        competitor_analysis = await analyze_competitors(company)
        parsed["competitors"] = competitor_analysis

        parsed["meta"] = parsed.get("meta", {})
        parsed["meta"].setdefault("last_updated", now_date_str())
        parsed["meta"]["diagnostics"] = diagnostics
        return {"markdown": generated, "json": parsed}

    chunks = chunk_texts(aggregated, text_key="text", max_chars=700)
    diagnostics["num_chunks"] = len(chunks)
    texts = [c["text"] for c in chunks][:200]
    diagnostics["num_texts_embedded"] = len(texts)

    query_emb = (await cohere_embed([company]))[0] if COHERE_API_KEY else [0.0]
    chunk_embs = await cohere_embed(texts) if COHERE_API_KEY else [[0.0] for _ in texts]
    diagnostics["num_chunk_embs"] = len(chunk_embs) if chunk_embs else 0

    selected = top_k_by_similarity(query_emb, chunk_embs, chunks, k=min(top_k, len(chunk_embs)))
    diagnostics["selected_examples"] = [{"chunk_preview": s["chunk"][:300], "score": s.get("score", 0)} for s in selected[:5]]

    prompt = build_prompt(company, selected)
    generated = await cohere_generate(prompt, max_tokens=1200)

    parsed = extract_json_payload(generated)
    if parsed is None:
        parsed = extract_arrays_from_text(generated, company=company)
    if parsed is None:
        repaired = await repair_json_with_model(generated, company)
        if repaired:
            parsed = repaired
    if parsed is None:
        swot_from_text = parse_swot_from_text(generated, company)
        parsed = {
            "company": company,
            "executive_summary": "",
            "swot": swot_from_text,
            "meta": {"last_updated": now_date_str(), "data_sources": ["model_text"], "notes": "parsed_from_text_fallback"}
        }

    # unwrap if the model returned JSON string inside a 'text' field
    parsed = unwrap_text_json(parsed)

    # normalize and ensure shape
    parsed = normalize_parsed_swot(parsed, company)

    # Ensure company name is set correctly
    company_field = parsed.get("company")
    if not company_field or company_field == "Unknown" or isinstance(company_field, dict):
        parsed["company"] = company

    swot = parsed.get("swot", {})
    # Don't add placeholder text - keep only real analysis
    parsed["swot"] = swot
    # Add sentiment analysis
    sentiment_analysis = await analyze_sentiment(company, selected)
    parsed["sentiment"] = sentiment_analysis

    # Add competitor analysis
    competitor_analysis = await analyze_competitors(company)
    parsed["competitors"] = competitor_analysis

    parsed["meta"] = parsed.get("meta", {})
    parsed["meta"].setdefault("last_updated", now_date_str())
    parsed["meta"].setdefault("data_sources", ["NewsAPI" if NEWSAPI_KEY else "none", "Twitter" if TWITTER_BEARER else "none", "Reddit"])
    parsed["meta"]["diagnostics"] = diagnostics
    return {"markdown": generated, "json": parsed}

# wrapper to show tracebacks
async def run_analyze_with_trace(company: str, lookback_days: int = LOOKBACK_DEFAULT, top_k: int = 10):
    try:
        return await run_analyze(company, lookback_days, top_k)
    except Exception as e:
        tb = traceback.format_exc()
        try:
            st.error("Exception during analysis — full traceback below (copy for debugging):")
            st.code(tb)
        except Exception:
            pass
        return {"markdown": f"Error during analysis: {str(e)}", "json": {"company": company, "executive_summary": "", "swot": {}, "meta": {"last_updated": now_date_str(), "notes": "error"}}}

# === Streamlit UI ===
st.title("SaaS SWOT — Real-time (Streamlit MVP)")

with st.sidebar:
    st.header("Settings")
    lookback_days = st.number_input("Lookback days", min_value=7, max_value=365, value=LOOKBACK_DEFAULT)
    top_k = st.slider("Top-K chunks to consider", min_value=3, max_value=30, value=10)
    cache_minutes = st.number_input("Cache minutes", min_value=0, max_value=1440, value=15)
    st.markdown("---")
    st.write("Sources:")
    st.write(f"- NewsAPI: {'✅' if NEWSAPI_KEY else '❌'}")
    st.write(f"- Twitter: {'✅' if TWITTER_BEARER else '❌'}")
    st.write(f"- Cohere: {'✅' if COHERE_API_KEY else '❌'}")

    debug_company = st.text_input("Debug company (for fetcher test)", value="Adobe")
    st.markdown("## Debug tools")
    if st.button("Test fetchers now"):
        try:
            news = asyncio.run(fetch_newsapi(debug_company, lookback_days))
            tweets = asyncio.run(fetch_twitter(debug_company, lookback_days))
            reddit = asyncio.run(fetch_reddit(debug_company, lookback_days))
            st.write("News returned:", len(news))
            st.write("Twitter returned:", len(tweets))
            st.write("Reddit returned:", len(reddit))
            if news:
                st.markdown("### Sample News article (first):")
                st.write(news[0])
            if tweets:
                st.markdown("### Sample Tweet (first):")
                st.write(tweets[0])
            if reddit:
                st.markdown("### Sample Reddit (first):")
                st.write(reddit[0])
        except Exception as ex:
            st.error(f"Fetcher test failed: {ex}")

    if st.button("Test Cohere Embed (quick)"):
        try:
            test_emb = asyncio.run(cohere_embed(["hello world"]))
            st.write("Embed result length:", len(test_emb), "vector length (first):", len(test_emb[0]) if test_emb else None)
        except Exception as ex:
            st.error(f"Embed test failed: {ex}")

company_input = st.text_input("Enter SaaS company name", placeholder="e.g., AcmeSaaS")
analyze_btn = st.button("Analyze (real-time)")

@st.cache_data(ttl=60*60)
def cached_analyze_key(company: str, lookback: int, top_k_val: int):
    return {"company": company, "lookback": lookback, "top_k": top_k_val}

if analyze_btn and company_input:
    key = f"{company_input}|{lookback_days}|{top_k}"
    st.session_state["last_query"] = key
    with st.spinner("Fetching & analyzing..."):
        try:
            result = asyncio.run(run_analyze_with_trace(company_input, lookback_days, top_k))
        except Exception as e:
            st.error(f"Error during analysis: {e}")
            result = None

    if result:
        markdown = result["markdown"]
        parsed = result["json"]

        st.subheader(f"SWOT Analysis: {parsed.get('company', normalize_company(company_input))}")
        sw = parsed.get("swot", {})
        cols = st.columns(4)
        categories = ["strengths", "weaknesses", "opportunities", "threats"]
        for col, cat in zip(cols, categories):
            col.markdown(f"### {cat.capitalize()}")
            items = sw.get(cat, [])
            for it in items:
                if isinstance(it, dict):
                    point = it.get("point", str(it))
                    score = it.get("score", "Unknown")
                    trend = it.get("trend", "Unknown")
                    source = it.get("source", "Unknown")
                    date = it.get("date", "")

                    # Display the point with better formatting
                    col.write(f"• **{point}**")

                    # Create a more detailed caption with score visualization
                    if isinstance(score, (int, float)):
                        score_color = "🟢" if score >= 70 else "🟡" if score >= 50 else "🔴"
                        col.caption(f"{score_color} {score} • {trend} • {source} • {date}")
                    else:
                        col.caption(f"50 • {trend} • {source} • {date}")
                else:
                    col.write(f"• **{str(it)}**")
                    col.caption("50 • Unknown • model_text • 2025-09-13")

        # Executive Summary with enhanced insights
        exec_sum = parsed.get("executive_summary", "")
        sentiment = parsed.get("sentiment", {})
        competitors = parsed.get("competitors", {})

        if exec_sum:
            st.info(f"**Executive Summary:** {exec_sum}")
        else:
            # Generate a summary based on available data
            sentiment_text = f"Current market sentiment is {sentiment.get('overall_sentiment', 'neutral').lower()}"
            position_text = f"with a {competitors.get('market_position', 'established').lower()} position"
            summary = f"{parsed.get('company', 'Company')} maintains {position_text} in the market. {sentiment_text} based on recent analysis."
            st.info(f"**Executive Summary:** {summary}")

        # Add key metrics row
        if sentiment or competitors:
            st.markdown("### Key Metrics")
            metric_cols = st.columns(4)
            with metric_cols[0]:
                st.metric("Sentiment Score", f"{sentiment.get('sentiment_score', 50)}/100")
            with metric_cols[1]:
                st.metric("Market Position", competitors.get('market_position', 'Unknown'))
            with metric_cols[2]:
                st.metric("Market Share", competitors.get('market_share', 'Unknown'))
            with metric_cols[3]:
                total_swot_items = sum(len(sw.get(cat, [])) for cat in ["strengths", "weaknesses", "opportunities", "threats"])
                st.metric("SWOT Points", total_swot_items)

        with st.expander("Sentiment Summary & Trend (hidden)"):
            sentiment = parsed.get("sentiment", {})
            if sentiment:
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Overall Sentiment", sentiment.get("overall_sentiment", "Unknown"))
                    st.metric("Sentiment Score", f"{sentiment.get('sentiment_score', 0)}/100")
                with col2:
                    st.metric("Trend", sentiment.get("trend", "Unknown"))
                    st.metric("Positive Mentions", sentiment.get("positive_mentions", 0))
                with col3:
                    st.metric("Negative Mentions", sentiment.get("negative_mentions", 0))
                    st.metric("Neutral Mentions", sentiment.get("neutral_mentions", 0))
                st.write("**Analysis:**", sentiment.get("analysis", "No analysis available"))
            else:
                st.write("No sentiment analysis available")

        with st.expander("Competitor Comparison"):
            competitors = parsed.get("competitors", {})
            if competitors:
                st.write(f"**Market Position:** {competitors.get('market_position', 'Unknown')}")
                st.write(f"**Market Share:** {competitors.get('market_share', 'Unknown')}")

                st.subheader("Main Competitors")
                main_comps = competitors.get("main_competitors", [])
                if main_comps:
                    for i, comp in enumerate(main_comps, 1):
                        st.write(f"{i}. {comp}")

                col1, col2 = st.columns(2)
                with col1:
                    st.subheader("Competitive Advantages")
                    advantages = competitors.get("competitive_advantages", [])
                    for adv in advantages:
                        st.write(f"• {adv}")

                with col2:
                    st.subheader("Competitive Threats")
                    threats = competitors.get("competitive_threats", [])
                    for threat in threats:
                        st.write(f"• {threat}")

                st.write("**Analysis:**", competitors.get("analysis", "No analysis available"))
            else:
                st.write("No competitor analysis available")

        with st.expander("Raw Citations / JSON_PAYLOAD"):
            st.code(parsed, language="json")

        with st.expander("Model Generated Markdown (raw)"):
            st.code(markdown)

        st.download_button("Download JSON", data=json.dumps(parsed, indent=2), file_name=f"{normalize_company(company_input)}_swot.json", mime="application/json")

else:
    st.info("Enter a company name and click Analyze to run a real-time SWOT. The app fetches NewsAPI / Twitter / Reddit and uses Cohere for RAG. Ensure API keys are set in .env.")
